<div *ngIf="item" class="row rowItem" (click)="openDialogItem()" [ngClass]="{ notAvailable: !itemAvailable }">
  <div class="col-10 col-sm-10 col-md-9 itemCol">
    <div class="itemName">
      {{ item.Name }}
    </div>
    <div *ngIf="itemAvailable" class="itemPrice">${{ item.Price | number : '1.2-2' }}</div>
    <div *ngIf="!itemAvailable" class="itemPrice">
      {{ availabilityText }}
    </div>
  </div>

  <div class="col-2 col-sm-2 col-md-3 itemCol">
    <img
      *ngIf="ImageUrl"
      class="itemPicture"
      height="56"
      width="56"
      alt="Item Image"
      [src]="GetUrlImage()"
      (error)="onImageError()"
      async
    />
    <div *ngIf="!ImageUrl" class="noPictureItem">
      <p>No photo</p>
    </div>
  </div>
</div>
